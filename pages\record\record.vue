<template>
	<view class="record-container">
		<view class="content">
			<text class="title">记录页面</text>
			<text class="description">这里是记录功能页面，敬请期待...</text>
		</view>
		
		<!-- 底部导航栏 -->
		<TabBar currentPage="record" />
	</view>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
	components: {
		TabBar
	},
	data() {
		return {
			
		}
	},
	onLoad() {
		
	}
}
</script>

<style scoped>
.record-container {
	width: 100%;
	min-height: 100vh;
	background-color: #F2282D;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

.content {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 100rpx 60rpx;
}

.title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 48rpx;
	font-weight: 700;
	color: #FFFFFF;
	margin-bottom: 40rpx;
	text-align: center;
}

.description {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 32rpx;
	font-weight: 400;
	color: #FFFFFF;
	text-align: center;
	line-height: 1.5;
}
</style>
